FROM dwchiang/nginx-php-fpm:8.2.9-fpm-alpine3.17-nginx-1.24.0
LABEL authors="qeezer"

RUN sed -i 's/dl-cdn.alpinelinux.org/mirror.tuna.tsinghua.edu.cn/g' /etc/apk/repositories
RUN docker-php-ext-install -j16 bcmath \
    && apk add curl autoconf make gcc g++ zlib-dev libpng-dev jpeg-dev freetype-dev \
    && docker-php-ext-install -j16 mysqli \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j16 gd \
    && pecl install redis \
    && docker-php-ext-enable redis

# 复制应用代码
COPY . /var/www/ju-skeleton

# 切换到应用目录
WORKDIR /var/www/ju-skeleton

# 下载 juposer
RUN curl -o /usr/local/bin/juposer http://php.registry.jishu666.com/juposer && chmod +x /usr/local/bin/juposer

# 安装依赖
RUN juposer install --no-dev --optimize-autoloader

