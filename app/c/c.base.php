<?php

class c_base
{
    public function __construct()
    {
        $this->uid = x_userid();//会员id
        $this->u = $this->uid > 0 ? x_user() : []; //用户信息
    }


    //是否post请求
    public function is_post()
    {
        if (BENDI != 1 && !is_post()) {
            ar('非法请求!', -1003);
        }
    }

    //记录请求信息
    public function __destruct()
    {
        $lx = 'user';
        if (issetx('duan', $_ENV)) {
            $lx = $_ENV['duan'];
        }
        $jilog = USER_LOG;//是否记录用户访问日志
        if ($lx == 'admin') {
            $jilog = x_admin::user_log;
        }
        if ($jilog == 1) {
            user_log($this->uid, $lx);//请求日志
        }
    }

    // oss文件下载/显示
    public function oss_file()
    {
        $filepath = re('filepath');
        $path_arr = explode('.', $filepath);
        $path_ext = strtolower(end($path_arr));
        $exts  = RX('download', 'exts');
        if (empty($filepath) || !in_arr($path_ext, $exts)) {
            ajaxReturn(['code' => -1, 'msg' => '参数错误']);
        }
        $re = D('m_base')->show_oss_file($filepath);
        ajaxReturn($re);
    }
}
