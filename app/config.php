<?php

//核心配置
//define('SESSION_AUTO',1);               //是否自动开启session
//define('JU',ROOT."ju".DS);              //框架目录
// define('LOG',ROOT.'log'.DS);            //普通日志目录
//define('LOGX',ROOT.'logx'.DS);          //特殊日志目录
//define('EXT',APP.'ext'.DS);             //函数扩展目录
//define('LIB',APP.'lib'.DS);             //类扩展目录
//define('APP_X',APP.'x'.DS);             //APP设置扩展目录
//define('VIEW_PATH',APP.'v'.DS);         //模板文件目录
// define('TEMP',LOG.'v'.DS);              //模板编译目录
//define('D_C','main');                   //默认控制器
//define('D_A','index');                  //默认方法

//多语言设置
//define('LANG',1);                       //开启多语言
//define('LANG_LIST',['zh-cn','en']);     //语言列表
//define('LANG_AUTO',true);               //自动检测语言

//其他设置
//define('HC_PRE','hcpr1e');              //页面缓存值前缀 方便一键清空

define('XPATH', ROOT.'x'.DS); //重要敏感配置项目录
include_once XPATH."db.php"; //加载敏感配置 db,redis等

defined('SESSION_LX') || define('SESSION_LX', false); // session 类型, false 不做特殊处理，1 使用 http Host，其他值使用自定义

//项目默认配置,支持参数的正则匹配 格式 <字段名[正则表达式]> 例如 <id[\d]>
$config = [
    'debug'   => BENDI,
    'trace'   => BENDI,
    'lang'    => 'zh-cn',//多语言开启时的默认语言
    'rewrite' => [
        //'https://<uid>.ju3.com' => 'main/demo',//域名泛解析 传参uid  *使用完整域名的路由规则需前置
//		'/' => '{{D_C}}/{{D_A}}',
        //自定义规则
        'base/ossfile/<filepath[*]>'  => 'base/oss_file',//公共ossfile路由

        'admin/<a>'   => 'admin/main/<a>',
        'admin'       => 'admin/main/index',
        '<c>/<a>'     => '<c>/<a>',
        '<c>'         => '<c>/{{D_A}}',
        '<m>/<c>/<a>' => '<m>/<c>/<a>',
    ],
];

//多域名配置,根据域名配置附加规则
$domain = [
    'a.com' => [
        'debug' => BENDI,
        'trace' => BENDI,
    ],
];

$_globals = isset($domain[$_SERVER["HTTP_HOST"]]) && is_array($domain[$_SERVER["HTTP_HOST"]]) ? array_merge($config, $domain[$_SERVER["HTTP_HOST"]]) : $config;
foreach ($_globals as $key => $value) {
    $GLOBALS[$key] = $value;
}
unset($_globals);

//数据库允许排序字段
$_ENV['dbsort'] = [
    'jm_user'   => 'uid,dlsj,tjsj',//会员表
    'jm_log'    => 'id,lx,sj,ip,uid',//日志表
    'jm_rb_log' => 'id,cz_lx,sj,ip,uid',//日志表
    'jm_qx'     => 'id,px',
    'jm_qxz'    => 'id,px',
    'jm_config' => 'id,tjsj,gxsj',
    'jm_cronrwx' => 'id,sj,tjsj,gxsj,sbsj',
];
//ID连贯性：回滚日志中不记录的表
$_ENV['db_rb_bjl_tb'] = [

];
