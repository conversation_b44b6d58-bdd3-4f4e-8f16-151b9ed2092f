<?php

//获取用户session信息
function x_user($key = '', $lx = 'user')
{
    //p($_SESSION);
    static $user;
    if (empty($user[$lx])) {
        $user[$lx] = session_get([$lx]);
    }
    if (empty($key)) {
        return $user[$lx];
    } else {
        return $user[$lx][$key] ?? '';
    }
}

//获取用户id
function x_userid()
{
    return ints(session_get(['user','uid']));
}

function x_adminid()
{
    if (ADMIN_TOKEN_TIME) {
        $token = re('server.HTTP_TOKEN');
        $hcm = x_rk::LOGIN_TOKEN_QZ.$token;
        $value = S($hcm);
        if (!empty($value)) {
            S($hcm, $value, ADMIN_TOKEN_TIME);
            return ints($value['admin']['uid']);
        }
        return 0;
    }
    return ints(session_get(['admin','uid']));
}

//判断用户是否拥有某些权限
function has_qx($uid, $map = [])
{
    if (ints($uid) < 1 || !$map) {
        return [];
    }
    $action = [];
    $qx_mca = D('m_admin_qx')->get_zh_qx($uid);
    if ($qx_mca[0] == 'admin') {
        $qx_mca = array_values($map);
    }
    foreach ($map as $k => $mca) {
        $action[$k] = (int)in_arr($mca, $qx_mca);
    }
    return $action;
}

//读取系统配置
function RC($name, $hc = 1)
{
    static $return = [];
    if (!isset($return[$name]) || $hc == -1) {
        $return[$name] = D('m_base')->get_config($name, $hc);
    }
    return $return[$name];
}

//文件日志
function user_log($uid, $lx = 'user', $path = '')
{
    if (empty($path)) {
        if ($lx == 'user') {
            $path = LOGX.'user_log'.DS.date('Y').DS.date('m').DS.date('d').DS.date('H').".txt";//按小时分割
        } else {
            $path = LOGX."{$lx}_log".DS.date('Ymd').'/'.$uid.'_'.date('Ymd').'.txt';
        }
    }
    return D('m_base')->user_log($uid, $path, $_ENV['recode']);
}

//日志记录
function xlog($fname, $sm = '', $bs = '', $lx = 0)
{
    return D('m_base')->xlog($fname, $sm, $bs, $lx);
}

//字符包含2 左右加,
function instr2($str, $need)
{
    return stripos(','.$str.',', ','.$need.',') !== false ? 1 : 0;
}

//返回字符串中匹配的字母和数字
function yggl($t0)
{
    return preg_replace("#[^a-zA-Z0-9_,]#", "", $t0);
}

//请求信息
function req()
{
    $fail_msg = "{$_SERVER['REQUEST_METHOD']}   {$_SERVER['REQUEST_URI']}";
    if ($_SERVER['REQUEST_METHOD'] == "POST") {
        $data = $_POST;
        unset($data['mm'],$data['yzm'],$data['sj_yzm'],$data['qr_mm']);
        $fail_msg .= "   ".http_build_query($data);
    }

    $header = PHP_EOL."---------------------------------".PHP_EOL;
    foreach ($_SERVER as $name => $value) {
        if (substr($name, 0, 5) == 'HTTP_' && strtoupper($name) != 'HTTP_COOKIE') {
            $header .= str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5))))).":".$value.PHP_EOL;
        }
    }
    $fail_msg .= $header;
    return glwb($fail_msg);
}

//中文字符串截取
function cn_substr($str, $start = 0, $length = 100, $charset = "utf-8", $suffix = true)
{
    $is_no_long = 0;
    if (function_exists("mb_strlen")) {
        $is_no_long = mb_strlen($str, $charset) <= $length;
    } elseif (function_exists("iconv_strlen")) {
        $is_no_long = iconv_strlen($str, $charset) <= $length;
    } else {
        switch ($charset) {
            case 'utf-8':
            case 'UTF8':
                $char_len = 3;
                break;
            default:
                $char_len = 2;
        }
        $is_no_long = strlen($str) <= ($length * $char_len);
    }
    //小于指定长度，直接返回
    if ($is_no_long) {
        return $str;
    }
    if (function_exists("mb_substr")) {
        $slice = mb_substr($str, $start, $length, $charset);
    } elseif (function_exists('iconv_substr')) {
        $slice = iconv_substr($str, $start, $length, $charset);
    } else {
        $re['utf-8'] = "/[\x01-\x7f]|[\xc2-\xdf][\x80-\xbf]|[\xe0-\xef][\x80-\xbf]{2}|[\xf0-\xff][\x80-\xbf]{3}/";
        $re['gb2312'] = "/[\x01-\x7f]|[\xb0-\xf7][\xa0-\xfe]/";
        $re['gbk'] = "/[\x01-\x7f]|[\x81-\xfe][\x40-\xfe]/";
        $re['big5'] = "/[\x01-\x7f]|[\x81-\xfe]([\x40-\x7e]|\xa1-\xfe])/";
        preg_match_all($re[$charset], $str, $match);
        $slice = join("", array_slice($match[0], $start, $length));
    }
    if ($suffix) {
        return $slice."…";
    }
    return $slice;
}

//记录日志时数组转换
function arrlog($arr, $fg = ':', $fg2 = ',')
{
    $arr = glwb($arr);
    if (is_array($arr)) {
        $arr2 = [];
        foreach ($arr as $k => $v) {
            $v = arrlog($v, $fg);
            $arr2[] = "{$k}{$fg}{$v}";
        }
        $arr = implode($fg2, $arr2);
    }
    return $arr;
}

//生成密码
function md5pass($pass, $salt = '')
{
    return md5(substr(md5($pass), 0, 10).$salt);
}

//生成csrftoken
function token_sc($expire = 300)
{
    $token = session_get(['cstoken']);
    $expire_time = session_get(['cstoken_expire']);
    if (strlen($token) < 16 || $expire_time < time()) {
        $token = suiji(16);
        session_set(['cstoken' => $token]);
        session_set(['cstoken_expire' => time() + $expire]);
    }
    return $token;
}

//验证csrftoken,$del为1=删除，0=不删除, $expire验证通过后延长有效期
function token_yz($str = "", $del = 1, $expire = 300)
{
    if (DEBUG && $str == "test") {
        return true;
    }
    $str = isset($_SERVER['HTTP_CSRFTOKEN']) ? $_SERVER['HTTP_CSRFTOKEN'] : $str;
    if (empty($str) || $str != token_sc()) {
        if ($del == 1) {
            token_rm();
        }
        return false;
    }
    //token过期
    if (session_get(['cstoken_expire']) < time()) {
        token_rm();
        return false;
    }
    //验证通过,强制删除
    if ($del == 1) {
        token_rm();
    } else {//验证通过,不删除则延长有效期
        session_set(['cstoken_expire' => time() + $expire]);
    }
    return true;
}

//手动删除csrftoken
function token_rm()
{
    @session_del(['cstoken']);
    @session_del(['cstoken_expire']);
}

/**
 * 获取文件信息
 * @param $filename
 * @return array
 */
function get_file_info($filename)
{
    $filename = glwb($filename);
    if (empty($filename)) {
        return [];
    }
    $filename_arr     = explode('?', $filename);
    $info             = pathinfo(current($filename_arr));
    $info['filepath'] = $info['dirname'] . '/' . $info['basename'];
    $info['query']    = '';
    $info['ext']      = $info['extension'];
    if (issetx(1, $filename_arr)) {
        $query = $filename_arr[1];
        parse_str($query, $query_arr);
        $info['query'] = $query_arr;
    }
    return $info;
}

/**
 * chttp 请求file，边请求边输出
 *
 * @param $url
 * @param int $timeout
 * @return void
 */
function chttp_file($url, $timeout = 10)
{
    $timeout = ints($timeout);
    if ($url == "") {
        exit("URL不能为空!");
    }
    $header = [];
    ob_clean();
    $ch     = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_ENCODING, "gzip");

    //SSL
    if (substr($url, 0, 8) === 'https://') {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    }
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, (int)$timeout); // 超时时间
    curl_setopt($ch, CURLOPT_TIMEOUT, (int)$timeout);

    // 设置写入函数
    curl_setopt($ch, CURLOPT_WRITEFUNCTION, function ($ch, $str) {
        echo $str;  // 直接输出数据
        ob_flush(); // 刷新输出缓冲
        flush();
        return strlen($str); // 返回已处理的字节数
    });

    // 执行
    curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return $httpCode;
}

//自定义推送消息方法
//function tsxx($bs,$msg) {
//    $bs  = glwb($bs);
//    $msg = glwb($msg);
//    if (!in_array($bs,['db_bug','file_bug'])){
//        return ['code' => -1,'msg' => '参数错误'];
//    }
//    return ddmsg::send($bs, $msg);
//}
