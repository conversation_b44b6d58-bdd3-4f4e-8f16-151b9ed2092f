<?php

//钉钉群机器人消息推送
//接入文档:https://developers.dingtalk.com/document/robots/custom-robot-access
class ddmsg
{
    /*
        robot: 机器人名字(系统参数中配置) 或  直接传机器人配置数组['secret'=>'','token'=>'']
        msg : 消息内容 默认传字符串则问text类型消息,传数组时支持钉钉支持的所有类型
        例如: ["msgtype"=>"markdown","markdown"=>["title"=>"杭州天气","text"=> "#### 西北风1级，空气良89，相对温度73%"]]
        at : all=@所有人,'手机号码'=@指定人,@多个人手机号码用逗号隔开即可
    */
    public static function send($robot = '', $msg, $at = '')
    {
        if (!$robot) {
            return ['code' => -1,'msg' => '缺少参数robot!'];
        }
        $set = is_string($robot) ? RC('dd_robots')[$robot] : $robot;
        if (!$set) {
            return ['code' => -1,'msg' => '机器人配置不存在!'];
        }

        //@人
        $at_param = [];
        if (!empty($at)) {
            if ($at === 'all') {
                $at_param['isAtAll'] = 'true';
            } else {
                $at_param['atMobiles'] = explode(',', $at);
                if (is_array($msg) && $msg['msgtype'] == 'markdown') {
                    $msg['markdown']['text'] .= "  \n---\n @".implode(",@", $at_param['atMobiles']);
                }
            }
        }
        $url = 'https://oapi.dingtalk.com/robot/send?access_token='.$set['token'];
        $data = ['at' => $at_param];

        if (isset($set['secret'])) {//加签验证模式追加签名
            $url .= self::sign($set['secret']);
        }

        //自定义消息类型
        if (is_array($msg)) {
            $data = array_merge($data, $msg);
        }
        //text消息
        else {
            //关键字验证模式 追加关键字
            if (isset($set['key'])) {
                $msg = $set['key'].$msg;
            }
            $data['msgtype'] = 'text';
            $data['text']['content'] = $msg;
        }
        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        $pdata = [
            'url' => $url,
            'do' => 'post',
            'data' => $data,
            'time' => 20,
            'qt' => ['Content-Type: application/json;charset=utf-8'],
            'code' => 1,
        ];
        $res = chttp($pdata);
        list($res['code'], $res['content']) = [$res[0],$res[1]];
        if ($res['code'] == 200) {
            $content = json_decode($res['content'], true);
            if ($content['errcode'] != 0) {
                $re = ['code' => -1,'msg' => "{$content['errcode']}-{$content['errmsg']}"];
            } else {
                $re = ['code' => 1,'msg' => $content['errmsg']];
            }
        } else {
            $re = ['code' => -2,'msg' => "请求错误-".$res['code']];
        }
        if ($re['code'] != 1) {
            _log('ddmsg', "\r\n请求:\r\n".json_encode($pdata, JSON_UNESCAPED_UNICODE)."\r\n返回:\r\n{$res['content']}");
        }
        return $re;
    }

    //加签
    public static function sign($secret)
    {
        list($s1, $s2) = explode(' ', microtime());
        $timestamp = (float)sprintf('%.0f', (floatval($s1) + floatval($s2)) * 1000);
        $data = $timestamp."\n".$secret;
        $signStr = base64_encode(hash_hmac('sha256', $data, $secret, true));
        $signStr = utf8_encode(urlencode($signStr));
        return "&timestamp=$timestamp&sign=$signStr";
    }

}
