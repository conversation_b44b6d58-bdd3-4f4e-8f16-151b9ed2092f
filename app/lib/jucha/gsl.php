<?php
//google收录

class gsl extends base
{

    private $cookie_list = 'google_cookies2';//cookie队列
    private $cookie_sycs = 'cookie_usage2';//使用次数
    private $cookie_maxnum = 120;

    public function __construct()
    {
        parent::__construct();
        $this->redis = lian_redis();
    }

    public function cha($d = [], $cs = 0)
    {
        $maxcs = $d['cs'] ?: 1;//最大重试次数
        $ym = self::ymyz($d['ym']);
        $fs = '';
        $diqulist = ['HK', 'MO', 'KH', 'KR', 'TH', 'IN', 'ID', 'SG', 'PH', 'VN', 'MY', 'NZ', 'TW'];//代理地区
        $diqu = $d['diqu'] ?: $diqulist[array_rand($diqulist)];
        $daili = ['pr.roxlabs.cn:4600', 'user-c4r1rr-region-' . strtolower($diqu) . ':RR3QI32CQK'];//roxlabs.cn

        if ($d['test2'] == 'y') {
            $dl = $this->getdl();
            $daili = ["http://{$dl}:32188", "juming:juming123456..."];//自建代理
        }
        if ($d['test3'] == 'y') {
            p($diqu);
            $daili = ['us.youzhiip.com:10000', 'userID-4431-orderid-17925-region-' . strtolower($diqu) . ':9as6FwzGLhy2w9kwni'];//aizhan_shanchen
        }
        $host = 'www.google.com';
        if (DEBUG) {
            $host = '***************';
            $daili = ['************:32188', 'juming:juming123456...'];
        }
        $pdata = [
            'url' => "https://{$host}/search?hl=en&safe=off&q=site%3A{$ym}&btnG=Search&gws_rd=cr",
            'qt' => [
                'host: www.google.com',
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                'Accept-Encoding: gzip, deflate',
                'Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            ],
            'llq' => 'Mozilla/5.0 (Windows NT 5.1; rv:23.0) Gecko/20100101 Firefox/21.0',
            'max' => $cs == 0 ? 100000 : 120000,
            'time' => 5,
            'daili' => $daili,
        ];
        $re = chttp($pdata);
        if ($d['test2']) {
            p($pdata);
            phtml($re);
        }
        if (empty($re)) {
            if ($cs < $maxcs) {
                return $this->cha($d, $cs + 1);
            }
            return ['code' => -100, 'msg' => '获取失败' . $cs];
        }
        if (stripos($re, '> - did not match any documents.') !== false && stripos($re, 'Make sure all words are spelled correctly.') !== false) {
            return ['code' => 1, 'msg' => 'ok', 'data' => 0];
        } elseif (stripos($re, '<div class="ezO2md"><div><div><a') !== false) {
            $sl_ok = explode('<div class="ezO2md"><div><div><a', $re);
            $msg = 'ok';
            $num = count($sl_ok) - 1;
            if ($num >= 10) {
                $fs = '2';
                $cha2 = $this->cha_new($d);
                $msg .= $fs;
                if ($cha2['code'] == 1) {
                    $num = $cha2['data'];
                    $msg .= "_" . $cha2['msg'];
                } else {
                    return $cha2;
                }
            }
            return ['code' => 1, 'msg' => $msg, 'data' => $num];
        }
        if ($cs < $maxcs) {
            return $this->cha($d, $cs + 1);
        }
        return ['code' => -2, 'msg' => '未知' . $cs];
    }

    //带cookie查谷歌
    public function cha_new($d = [], $cs = 0, $daili_zj = 0)
    {
        $ym = self::ymyz($d['ym']);
        $diqulist = ['HK', 'MO', 'KH', 'KR', 'TH', 'IN', 'ID', 'SG', 'PH', 'VN', 'MY', 'NZ', 'TW'];//代理地区
        $diqu = $d['diqu'] ?: $diqulist[array_rand($diqulist)];
        $daili = ['pr.roxlabs.cn:4600', 'user-c4r1rr-region-' . strtolower($diqu) . ':RR3QI32CQK'];//roxlabs.cn
        if ($d['test2'] == 'y' || $daili_zj == 1) {
            $dl = $this->getdl();
            $daili = ["http://{$dl}:32188", "juming:juming123456..."];//自建代理
        }
        $host = 'www.google.com';
        if (DEBUG) {
            $host = '***************';
            $daili = ['************:32188', 'juming:juming123456...'];
        }
        $cookie = $this->get_cookie();
        if ($d['test2']) {
            p($cookie);
        }
        if ($cookie['code'] != 1) {
            return $cookie;
        }
        $pdata = [
            'url' => "https://{$host}/search?hl=en&q=site%3A{$ym}&client=aff-cs-360se&ie=UTF-8&oe=UTF-8&dpr=2&igu=1&psi=" . time(),
            'qt' => [
                'host: www.google.com',
                'Accept-Encoding: gzip, deflate',
                'Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
                'authority: www.google.com',
                'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'cache-control: no-cache',
                'cookie: ' . $cookie['cookie'],// 使用动态获取的cookie
                'dnt: 1',
                'pragma: no-cache',
                'referer: https://www.google.com/',
                'rtt: 100',
                'sec-ch-prefers-color-scheme: dark',
                'sec-ch-ua: "Not-A.Brand";v="24", "Chromium";v="14"',
                'sec-ch-ua-arch: "arm"',
                'sec-ch-ua-bitness: "64"',
                'sec-ch-ua-full-version: "14.5.1070.0"',
                'sec-ch-ua-full-version-list: "Not-A.Brand";v="********", "Chromium";v="14.5.1070.0"',
                'sec-ch-ua-mobile: ?0',
                'sec-ch-ua-model: ""',
                'sec-ch-ua-platform: "macOS"',
                'sec-ch-ua-platform-version: "15.3.1"',
                'sec-ch-ua-wow64: ?0',
                'sec-fetch-dest: document',
                'sec-fetch-mode: navigate',
                'sec-fetch-site: same-origin',
                'sec-fetch-user: ?1',
                'upgrade-insecure-requests: 1',
                'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.95 Safari/537.36 QIHU 360EE',
            ],
            //'max'=>400000,
            'time' => 5,
            'daili' => $daili,
        ];
        $re = chttp($pdata);
        $re = str_replace(['\x22', '\x3d', '\x3e', '\x3c'], ['"', '=', '>', '<'], $re);
        if ($d['test2']) {
            p($pdata);
            phtml($re);
        }
        if (empty($re)) {
            if ($cs == 0) {
                return $this->cha_new($d, 1, 1);
            }
            return ['code' => -100, 'msg' => '获取失败'];
        }
        if (stripos($re, '<div id="result-stats">') !== false) {
            $x_sl = getstr($re, '<div id="result-stats">', '<');
            $x_sl = strtolower($x_sl);
            $x_sl = getstr($x_sl, 'about', 'result');
            $x_sl = str_replace(['.', ',', chr(9), chr(32)], '', $x_sl);
            return ['code' => 1, 'msg' => 'ok_' . $diqu, 'data' => _to_num($x_sl)];

            $regexp = ['<div id="result-stats">.*?About ([0-9,’]+) results<nobr>',
                '<div id="result-stats">.*?([0-9,’]+) results<nobr>',
                '<div id="result-stats">.*?About ([0-9,’]+) result<nobr>',
                '<div id="result-stats">.*?([0-9,’]+) result<nobr>'];
            foreach ($regexp as $x) {
                $cha = preg_match('/' . $x . '/i', $re, $match);
                if ($cha && count($match) > 1) {
                    $match[1] = str_replace(['.'], '', $match[1]);
                    return ['code' => 1, 'msg' => 'oks1', 'data' => _to_num($match[1])];
                }
            }
        } elseif (stripos($re, '<div id="extabar">') !== false) {
            $x_sl = getstr($re, '<div id="extabar">', '<');
            $x_sl = strtolower($x_sl);
            $x_sl = getstr($x_sl, 'about', 'result');
            $x_sl = str_replace(['.', ',', chr(9), chr(32)], '', $x_sl);
            return ['code' => 1, 'msg' => 'oks2_' . $diqu, 'data' => _to_num($x_sl)];

            $regexp = ['<div id="extabar">.*?About ([0-9,’]+) results<nobr>',
                '<div id="extabar">.*?([0-9,’]+) results<nobr>',
                '<div id="extabar">.*?About ([0-9,’]+) result<nobr>',
                '<div id="extabar">.*?([0-9,’]+) result<nobr>'];
            foreach ($regexp as $x) {
                $cha = preg_match('/' . $x . '/i', $re, $match);
                if ($cha && count($match) > 1) {
                    $match[1] = str_replace(['.'], '', $match[1]);
                    return ['code' => 1, 'msg' => 'ok', 'data' => _to_num($match[1])];
                }
            }
        } elseif (stripos($re, 'Our systems have detected unusual traffic from your computer network.') !== false) {
            if ($cs == 0) {
                //$this->del_cookie($cookie['cookie']);//删除无效cookie
                return $this->cha_new($d, 1, 1);
            }
            return ['code' => -1, 'msg' => '被拦截'];
        }
        return ['code' => -2, 'msg' => '未知'];
    }

    public function cha_old($d = [], $cs = 0)
    {
        $maxcs = $d['cs'] ?: 3;//最大重试次数
        $ym = self::ymyz($d['ym']);
        $diqulist = ['HK', 'MO', 'KH', 'KR', 'TH', 'IN', 'ID', 'SG', 'PH', 'VN', 'MY', 'NZ', 'TW'];//代理地区
        $diqu = $d['diqu'] ?: $diqulist[array_rand($diqulist)];
        G("ks_{$ym}_{$cs}");
        $daili = ['pr.roxlabs.cn:4600', 'user-c4r1rr-region-' . strtolower($diqu) . ':RR3QI32CQK'];//roxlabs.cn

        if ($d['test2'] == 'y') {
            $dl = $this->getdl();
            $daili = ["http://{$dl}:32188", "juming:juming123456..."];//自建代理
        }
        //$daili=['proxy.smartproxycn.com:1000','qwerasdjyg_area-'.$diqu.':qazwsx'];//smartproxy.cn
        $host = 'www.google.com';
        if (DEBUG) {
            $host = '**************';
            $daili = ['************:32188', 'juming:juming123456...'];
        }
        $pdata = [
            'url' => "https://{$host}/search?hl=en&safe=off&q=site%3A{$ym}&btnG=Search&gws_rd=cr",
            //'url'=>'http://myip.roxlabs.io',
            //'cookie'=>'',
            'qt' => [
                'host: www.google.com',
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                'Accept-Encoding: gzip, deflate',
                'Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            ],
            'llq' => 'Mozilla/5.0 (Windows NT 5.1; rv:23.0) Gecko/20100101 Firefox/21.0',
            'max' => $cs == 0 ? 100000 : 120000,
            'time' => 5,
            'daili' => $daili,
        ];
        /*if($d['plcs']==1){
            $pdata['url']='http://myip.roxlabs.io';
            $pdata['qt']=[];
        }*/

        $re = chttp($pdata);

        $fujia = [];

        if ($d['test2']) {
            p($pdata);
            phtml($re);
        }

        if ($d['test']) {
            $fujia = ['x' => $diqu, 't' => G("ks_{$ym}_{$cs}", "js_{$ym}_{$cs}", 3)];
        }

        /*if($d['plcs']==1){
            if(empty($re)){
                return '获取失败';
            }
            $this->ipx[]=explode('|',$re)[0];
            return $fujia['t']."|".$re;
        }*/

        if (empty($re)) {
            if ($cs < $maxcs) {
                return $this->cha($d, $cs + 1);
            }
            return ['code' => -100, 'msg' => '获取失败' . $cs] + $fujia;
        }

        if (stripos($re, '<div id="extabar">') !== false) {
            $regexp = ['<div id="extabar">.*?About ([0-9,’]+) results<nobr>',
                '<div id="extabar">.*?([0-9,’]+) results<nobr>',
                '<div id="extabar">.*?About ([0-9,’]+) result<nobr>',
                '<div id="extabar">.*?([0-9,’]+) result<nobr>'];
            foreach ($regexp as $x) {
                $cha = preg_match('/' . $x . '/i', $re, $match);
                if ($cha && count($match) > 1) {
                    return ['code' => 1, 'msg' => 'ok', 'data' => _to_num($match[1])] + $fujia;
                }
            }
        } elseif (stripos($re, '<div id="result-stats">') !== false) {
            $regexp = ['<div id="result-stats">.*?About ([0-9,’]+) results<nobr>',
                '<div id="result-stats">.*?([0-9,’]+) results<nobr>',
                '<div id="result-stats">.*?About ([0-9,’]+) result<nobr>',
                '<div id="result-stats">.*?([0-9,’]+) result<nobr>'];
            foreach ($regexp as $x) {
                $cha = preg_match('/' . $x . '/i', $re, $match);
                if ($cha && count($match) > 1) {
                    return ['code' => 1, 'msg' => 'ok', 'data' => _to_num($match[1])] + $fujia;
                }
            }
        } elseif (stripos($re, 'Our systems have detected unusual traffic from your computer network.') !== false) {
            //_log('gslx','被拦截');
            if ($cs < $maxcs) {
                return $this->cha($d, $cs + 1);
            }
            return ['code' => -1, 'msg' => '被拦截' . $cs] + $fujia;
        } elseif (stripos($re, '> - did not match any documents.') !== false && stripos($re, 'Make sure all words are spelled correctly.') !== false) {
            return ['code' => 1, 'msg' => 'ok', 'data' => 0];
        } elseif (stripos($re, '<div class="ezO2md"><div><div><a') !== false) {
            $sl_ok = explode('<div class="ezO2md"><div><div><a', $re);
            return ['code' => 1, 'msg' => 'ok', 'data' => count($sl_ok) - 1];
        }
        //_log('gsl',$re);
        if ($cs < $maxcs) {
            return $this->cha($d, $cs + 1);
        }
        return ['code' => -2, 'msg' => '未知' . $cs] + $fujia;
    }

    public function cookie_gl($d = [])
    {
        set_time_limit(0);//不超时
        ignore_user_abort(true);
        $hcm = 'google_cookie_gl';
        $suo = lock($hcm, 100);
        if ($suo == 0 && $d['test'] != 1) {
            return ['code' => -1, 'msg' => '系统繁忙,请重试!'];
        }
        //获取当前可用的 cookie 数量
        $cookieCount = $this->redis->lLen($this->cookie_list);
        $num = DEBUG ? 2 : $this->cookie_maxnum;//最少多少组cookie
        if ($cookieCount < $num && $d['test'] != 1) {
            $needCount = $num - $cookieCount;
            p("当前 Cookie 数量: {$cookieCount}, 需要补充: {$needCount}");

            //补充 cookie
            for ($i = 0; $i < $needCount; $i++) {
                $response = chttp(['url' => 'http://************:5000/get_cookie', 'time' => 60]);
                $re = json_decode($response, 1);
                if ($re && $re['message'] === 'Success' && $re['cookies']) {
                    $this->redis->rPush($this->cookie_list, $re['cookies']);
                    $this->redis->hSet($this->cookie_sycs, $re['cookies'], 0);//初始化使用次数
                    p("成功添加新 Cookie:{$re['cookies']}");
                } else {
                    if ($this->redis->lLen($this->cookie_list) < 80) {
                        $this->ddmsg("google_cookie当前总量:" . $this->redis->lLen($this->cookie_list) . "获取失败:{$response}");
                    }
                    p("获取 Cookie 失败: " . ($re['Success'] ?? '未知错误'));
                }
                //间隔 3 秒再获取下一个
                //sleep(1);
            }
            p("处理完成!");
        } else {
            p("当前 Cookie 数量:{$cookieCount},无需处理");
        }
        unlock($hcm, $suo);
        die;
    }

    //钉钉报警
    private function ddmsg($txt)
    {
        $hcm = 'google_ddmsg';
        if (!S($hcm)) {
            ddmsg($txt, '18939317670', 'qita');
            S($hcm, 1, 60);
        }
    }

    //获取cookie
    public function get_cookie()
    {
        //原有的获取逻辑保持不变
        $cookie = $this->redis->lIndex($this->cookie_list, 0);
        if ($cookie) {
            $sycs = $this->redis->hGet($this->cookie_sycs, $cookie);
            if ($sycs >= 38) {
                //使用次数达到上限，移除该 cookie
                $this->redis->lRem($this->cookie_list, $cookie, 1);
                $this->redis->hDel($this->cookie_sycs, $cookie);
                return $this->get_cookie(); //递归获取下一个
            }
            $this->redis->hIncrBy($this->cookie_sycs, $cookie, 1);//增加使用次数
            return ['code' => 1, 'cookie' => $cookie, 'sycs' => $sycs + 1];
        }
        return ['code' => -1, 'msg' => '无可以cookie'];
    }

    private function del_cookie($cookie)
    {
        $this->redis->lRem($this->cookie_list, $cookie, 1);
        $this->redis->hDel($this->cookie_sycs, $cookie);
    }

    //自建代理队列
    public function getdl()
    {
        $dliplb = $_ENV['xgdl'];
        $hcm = 'google_dl_3';
        if ($_GET['del'] == 1) {
            $this->redis->del($hcm);//删除列队
        }
        if ($_GET['chadl'] == 1) {
            p($this->redis->LRANGE($hcm, 0, 20000));//查看
        }
        $hc = $this->redis->RPOPLPUSH($hcm, $hcm);
        if (!$hc) {
            foreach ($dliplb as $v) {
                $this->redis->rpush($hcm, $v);
            }
            return $this->getdl();
        }
        return $hc;
    }

    public function cs($d = [])
    {
        die;
        for ($i = 0; $i < 100; $i++) {
            $re = $this->cha(['ym' => $i . 'qq.com', 'test' => 1, 'diqu' => $d['diqu'], 'plcs' => $d['plcs']], 0);
            p($re);
        }
    }


    public function search_bing($d = [])
    {
        $ym = self::ymyz($d['ym']);

        $dl = $this->getdl();
        $daili = ["http://{$dl}:32188", "juming:juming123456..."];//自建代理
        $pdata = [
            'url' => "https://www.bing.com/search?q={$ym}",
            'qt' => [
                'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language: zh-CN,zh;q=0.9',
                'cache-control: no-cache',
                'pragma: no-cache',
                'priority: u=0, i',
                'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile: ?0',
                'sec-ch-ua-platform: "macOS"',
                'sec-fetch-dest: document',
                'sec-fetch-mode: navigate',
                'sec-fetch-site: none',
                'sec-fetch-user: ?1',
                'upgrade-insecure-requests: 1',
                'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            ],
            //'max'=>400000,
            'time' => 5,
            'tz'=>1,
            'daili' => $daili,
        ];
        $re = chttp($pdata);
        p($re);
    }

}

