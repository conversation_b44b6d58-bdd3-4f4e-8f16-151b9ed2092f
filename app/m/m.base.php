<?php

//公用方法

class m_base
{
    public function hello()
    {
        return [
            'first' => 'hello',
            'second' => 'world'
        ];
    }


    //系统日志
    public function xlog($fname, $sm, $bs, $lx = 0)
    {
        $ip = getip();
        $data = [
            'uid' => x_adminid() ?: x_userid(),
            'ff'  => str_replace("::", '/', $fname),
            'lx'  => ints($lx),
            'bs'  => glwb($bs),
            'sm'  => cn_substr(glwb($sm), 0, 5000),
            'req' => req(),
            'sj'  => getsj(),
            'ip'  => $ip,
            'dq'  => ipgsd($ip),
        ];
        return M()->insert('jm_log', $data, 1);
    }

    //访问日志 可配合阿里云sls满足大数据分析需求
    public function user_log($uid, $path, $re = '')
    {
        if ($_SERVER['REQUEST_METHOD'] == "HEAD") {
            return false;
        }
        $uid = ints($uid);
        $ff = implode('/', MCA);
        if (instr2(RC('user_log_no'), $ff) > 0) {
            return false;
        }
        $data = '';
        if ($_SERVER['REQUEST_METHOD'] == "POST") {
            $pdata = $_POST;
            unset($pdata['callback'],$pdata['key'],$pdata['pass'],$pdata['mm'],$pdata['sign'],$pdata['old_mm'],$pdata['qr_mm']);
            $data = http_build_query($pdata);
        }
        $time = date('Y-m-d H:i:s');
        $ip = getip(1);
        $haoshi = G('justart', 'juendx', 3) * 1000;//执行总耗时_毫秒
        $log = "{$uid}\t{$time}\t{$ip}\t{$_SERVER['REQUEST_METHOD']}\t{$_SERVER['HTTP_HOST']}{$_SERVER['REQUEST_URI']}\t{$data}\t{$re}\t{$_SERVER['HTTP_REFERER']}\t{$_SERVER['HTTP_USER_AGENT']}\t{$ff}\t{$haoshi}\r\n";
        return wjxie($path, $log);
    }

    //获取系统配置项带缓存
    public function get_config($name, $hc = 1)
    {
        $hc = ints($hc);
        $hcm = x_rk::PRE_XT_CONFIG.glwb($name);
        $hctime = $hc == 1 ? 864000 : $hc;
        return jd(M()->key($hcm)->get_field('jm_config', 'data', ['name' => $name], $hctime));
    }

    //文件上传
    public function up_file($uid, $d)
    {
        $uid = ints($uid);
        $set = RX('up');
        $d['lx'] = $d['lx'] ?: 'img';//默认img类型
        if (!issetx($d['lx'], $set)) {
            return rs("上传类型异常!");
        }
        $set = $set[$d['lx']];
        $config = $set['config'];                         //配置项
        $form_name = $set['form_name'] ?: 'file';         //表单字段名
        $base64 = $set['base64'] == 1 ? 'base64' : false; //是否base64
        $save_ms = empty($set['save']) ? 0 : $set['save'];//文件存储模式 默认存本地
        $path = $set['path'] ? $set['path'] : '';         //额外路径

        $path_bd = "up/{$uid}/{$path}/";
        if ($save_ms == 0 || $save_ms == 1) {
            $config['upDir'] = ROOT."www/{$path_bd}";
        } else {
            $config['upDir'] = LOG.'temp/';
        }
        $up = new upload($config, $form_name, $base64);
        $up_info = $up->getFileInfo();
        if ($up_info['state'] == 'SUCCESS') {
            $url = "{$path_bd}{$up_info['path']}";
            if ($save_ms == 1 || $save_ms == 2) {
                $file_path = $config['upDir'].$up_info['path'];//文件地址
                $options   = [];
                if (issetx('ext', $up_info) && strtolower($up_info['ext']) == 'pdf') { //pdf 文件只允许下载
                    $options['headers'] = ['Content-Disposition' => 'attachment'];
                }
                $uposs = oss(OSS)->putObject(OSS['bucket'], $url, file_get_contents($file_path), $options);
                if ($save_ms == 2) {
                    @unlink($file_path);
                }
                if ($uposs['code'] == 1) {
                    return rs("上传成功!", 1, $url);
                } else {
                    return rs("上传失败![ERRCODE:2]");
                }
            }
            return rs("上传成功!", 1, '/'.$url);
        } else {
            return rs("上传失败:{$up_info['state']}!");
        }
    }

    /**
     * 文件显示/下载
     *
     * @param string $filepath 文件路径
     * @param int $fs 返回方式 1 文件流 2隐私连接
     * @param int $time
     * @return array|void
     */
    public function show_oss_file($filepath, $fs = 1, $time = 60)
    {
        $filepath  = glwb($filepath);
        $fs        = ints($fs);
        $time      = ints($time);
        $file_info = get_file_info($filepath);
        $fext      = $file_info['extension'];
        $exts      = RX('download', 'exts');//下载头
        $rule      = [
            ['in', [$fext, $exts], '文件格式错误！'],
            ['in', [$fs, [1, 2]], '方式异常！'],
            ['daxiao', [$time, 1, 3600], '文件有效时间异常！'],
        ];
        $ck        = check($rule);
        if ($ck !== true) {
            return rs("抱歉，{$ck}");
        }
        $wj_if = oss()->doesObjectExist(OSS['bucket'], $file_info['filepath']);
        if ($wj_if['data'] != 1) {
            return rs('抱歉，文件不存在！');
        }
        $option = [];
        if ($file_info['query']) {//判断是否有图片处理参数
            if (issetx('x-oss-process', $file_info['query'])) {
                $option['x-oss-process'] = $file_info['query']['x-oss-process'];
            }
        }
        $tmp_url_res = oss(OSS)->signUrl(OSS['bucket'], $file_info['filepath'], $time, 'GET', $option);
        //获取文件名
        if ($tmp_url_res['code'] !== 1) {
            return rs('抱歉，文件处理失败！');
        }
        $tmp_url = current($tmp_url_res['data']);
        if (empty($tmp_url)) {
            return rs('抱歉，文件地址获取失败！');
        }
        if ($fs == 2) {
            return rs('ok', 1, $tmp_url);
        }
        head_down(urlencode($file_info['basename']));
        chttp_file($tmp_url, $time);
        exit();
    }

    /**
     * 统一方法处理 url
     * @param $url string 文件地址
     * @param $fs int 0 base 1 user 2 admin
     * @return string
     */
    public function handle_oss_file_url($url, $fs = 0)
    {
        $url         = glwb($url);
        $fs          = ints($fs);
        $show_url_fs = RX('download', 'show_url_fs');
        if (empty($url) || !in_arr($fs, array_keys($show_url_fs)) || !D('check')->url($url)) {
            return '';
        }
        $oss_file_url = RX('user', 'fj_url');//ossfile 域名
        if (left($url, len($oss_file_url)) != "{$oss_file_url}") {
            return '';
        }
        $furl = str_replace("{$oss_file_url}", '', $url);
        if (!issetx($fs, $show_url_fs)) {
            return '';
        }
        $path = $show_url_fs[$fs];
        return '/' . $path . "/ossfile/{$furl}";
    }
}
