<?php

/**
 * REDIS KEY设置
 * x_rk::XXX 调用方式
 */

class x_rk
{
    /* @var 登录验证码 */
    public const LOGIN_YZM = 'LOGIN_YZM:';

    /* @var 登录防爆破 */
    public const LOGIN_FBP = 'LOGIN_FBP:';

    /* @var 帐号权限  */
    public const ZHQX = 'zhqx:';

    /* @var 帐号权限组  */
    public const ZH_QXZ = 'zh_qxz:';

    /* @var 权限组缓存  */
    public const PRE_FIND_QXZ = 'find_qxz:';

    /* @var 系统设置  */
    public const PRE_XT_CONFIG = 'xt_config:';

    //设置登录前缀
    public const LOGIN_TOKEN_QZ = 'login_token_qz:';

}
