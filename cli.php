#!/usr/bin/env php
<?php

$argv = $_SERVER['argv'];
$_SERVER['DOCUMENT_ROOT'] = __DIR__;
$_SERVER['REQUEST_URI'] = '/'.($argv[1] ?? '');
$_SERVER["SCRIPT_NAME"] = '/index.php';
$_SERVER['HTTP_HOST'] = 'localhost';
parse_str(parse_url($_SERVER['REQUEST_URI'],PHP_URL_QUERY) ?? '', $_GET);

$app_name = "app";                                    //项目目录名
define('DS',DIRECTORY_SEPARATOR);                     //路径分隔符
define('ROOT',dirname(__FILE__).DS);                  //上级目录
define('APP',ROOT.$app_name.DS);                      //项目目录
is_file(APP.'config.php') && require APP.'config.php';//项目配置文件
defined('JU') || define('JU',ROOT."/vendor/ju4/ju4-framework/src".DS);           //框架目录

$possiblePaths = [__DIR__.'/../../autoload.php', __DIR__.'/../autoload.php', __DIR__.'/vendor/autoload.php'];

foreach ($possiblePaths as $path) {
    if (file_exists($path)) {
        require $path;
        break;
    }
}

require JU.'ju.php';                //引入框架
