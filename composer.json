{"name": "ju4/ju4-skeleton", "description": "Ju4 framework skeleton.", "type": "project", "license": "MIT", "authors": [{"name": "juming", "email": "<EMAIL>"}], "minimum-stability": "stable", "require": {"ju4/ju4-framework": "^1.0"}, "require-dev": {"ju4/ju4-devtools": "^1.0"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "scripts": {"post-create-project-cmd": ["echo 'Running juposer install automatically...'", "juposer install"], "post-root-package-install": ["cp -r ./x_demo ./x"], "ju": ["php ./vendor/bin/ju"], "ju:publish": ["php ./vendor/bin/ju publish"]}}