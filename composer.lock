{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "685a136b2112a1ebafac5bc914937d6c", "packages": [{"name": "ju4/ju4-framework", "version": "1.0.5", "source": {"type": "git", "url": "********************:ju4/ju4-framework.git", "reference": "6229c2bca052c0461e81c8cad348de012e8e35e1"}, "dist": {"type": "tar", "url": "http://php.registry.jishu666.com//dist/ju4/ju4-framework/ju4-ju4-framework-6229c2bca052c0461e81c8cad348de012e8e35e1-zip-739c61.tar", "reference": "6229c2bca052c0461e81c8cad348de012e8e35e1", "shasum": "20d2ab3e4b61d3d2108bdcf4e854930c32a1b690"}, "require": {"php": ">=7.4"}, "type": "library", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Juming PHP Framework v4.", "homepage": "https://www.juming.com", "keywords": ["framework", "ju4", "juming"], "support": {"source": "http://git.jishu666.com/ju4/ju4-framework/-/tree/1.0.5", "issues": "http://git.jishu666.com/ju4/ju4-framework/-/issues"}, "time": "2025-07-28T17:29:47+08:00"}], "packages-dev": [{"name": "ju4/ju4-devtools", "version": "1.0.2", "source": {"type": "git", "url": "********************:ju4/ju4-devtools.git", "reference": "f220a9d8be929f7984d1d6b75d890492317c0984"}, "dist": {"type": "tar", "url": "http://php.registry.jishu666.com//dist/ju4/ju4-devtools/ju4-ju4-devtools-f220a9d8be929f7984d1d6b75d890492317c0984-zip-086e93.tar", "reference": "f220a9d8be929f7984d1d6b75d890492317c0984", "shasum": "5dfeea2dc15e5592fbb83587bde4aef97e6574ae"}, "bin": ["ju"], "type": "library", "autoload": {"psr-4": {"Ju4\\Ju4Devtools\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Ju4 framework development kits.", "support": {"source": "http://git.jishu666.com/ju4/ju4-devtools/-/tree/1.0.2", "issues": "http://git.jishu666.com/ju4/ju4-devtools/-/issues"}, "time": "2025-07-28T14:59:32+08:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "plugin-api-version": "2.6.0"}