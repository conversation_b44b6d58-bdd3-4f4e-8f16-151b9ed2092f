-- ----------------------------
-- Table structure for jm_log
-- ----------------------------
DROP TABLE IF EXISTS `jm_log`;
CREATE TABLE `jm_log`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `ff` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类_方法名',
  `lx` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '日志类型 0普通操作 1普通异常 2重要异常 99已处理异常',
  `bs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '特殊标识 id,域名等',
  `sm` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注信息',
  `req` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '请求信息',
  `sj` datetime DEFAULT NULL COMMENT '操作时间',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作ip',
  `dq` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地区',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_lx`(`lx`) USING BTREE,
  INDEX `idx_sj`(`sj`) USING BTREE,
  INDEX `idx_uid`(`uid`) USING BTREE,
  INDEX `idx_ff`(`ff`) USING BTREE,
  INDEX `idx_bs`(`bs`) USING BTREE
) ENGINE = InnoDB  CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of jm_log
-- ----------------------------
INSERT INTO `jm_log` VALUES (16, 1, 'm_admin_main/login', 0, '后台登录', 'admin__总管理员 登录成功!', 'POST   /admin/main/login   zh=admin&mm=111111&yzm=6666&sj_yzm=666666\n---------------------------------\nCookie:jm_show_page_trace=0|0; PHPSESSID=10hsdn5ctc1c54sibr77fv0ctg\nAccept-Language:zh-CN,zh;q=0.9\nAccept-Encoding:gzip, deflate\nReferer:http://ju3.com/admin/\nOrigin:http://ju3.com\nContent-Type:application/x-www-form-urlencoded; charset=UTF-8\nUser-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36\nX-Requested-With:XMLHttpRequest\nAccept:application/json, text/javascript, */*; q=0.01\nContent-Length:41\nConnection:keep-alive\nHost:ju3.com', '2023-05-27 08:50:00', '**********', '局域网');

-- ----------------------------
-- Table structure for jm_rb_log
-- ----------------------------
DROP TABLE IF EXISTS `jm_rb_log`;
CREATE TABLE `jm_rb_log` (
 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
 `uid` int(11) DEFAULT '0' COMMENT '用户ID',
 `ff` varchar(100) DEFAULT '' COMMENT '方法',
 `tb` varchar(50) DEFAULT '' COMMENT '表名',
 `cz_lx` tinyint(1) DEFAULT '0' COMMENT '操作类型',
 `trans_id` varchar(50) DEFAULT '0' COMMENT '事务ID',
 `rows` int(11) DEFAULT '0' COMMENT '影响行数',
 `sm` text COMMENT '说明',
 `rb_sql` longtext COMMENT '执行sql',
 `req` text COMMENT '请求数据',
 `sj` datetime DEFAULT NULL COMMENT '时间',
 `ip` varchar(50) DEFAULT '' COMMENT 'IP',
 `dq` varchar(255) DEFAULT '' COMMENT '地区',
 PRIMARY KEY (`id`) USING BTREE COMMENT '主键',
 KEY `idx_uid` (`uid`) USING BTREE,
 KEY `idx_ff` (`ff`) USING BTREE,
 KEY `idx_tb` (`tb`) USING BTREE,
 KEY `idx_cz_lx` (`cz_lx`) USING BTREE,
 KEY `idx_trans_id` (`trans_id`) USING BTREE,
 KEY `idx_ip` (`ip`) USING BTREE,
 KEY `idx_sj` (`sj`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='系统回滚日志'  ROW_FORMAT = Dynamic;

