<?php

$app_name = "app";                                    //项目目录名
define('DS', DIRECTORY_SEPARATOR);                     //路径分隔符
define('ROOT', dirname(__DIR__).DS);                  //上级目录
define('APP', ROOT.$app_name.DS);                      //项目目录
is_file(APP.'config.php') && require APP.'config.php';//项目配置文件
defined('JU') || define('JU', ROOT."/vendor/ju4/ju4-framework/src".DS);           //框架目录

require ROOT . '/vendor/autoload.php';
require JU . '/ju.php';
