<?php

//数据库配置 支持多个数据库
define('DB', '{"db_type":"mysql","dbname":"ju3","server":"127.0.0.1","username":"root","password":"mima","charset":"utf8mb4","port":3306}');

//redis
define('SERVER_REDIS_MASTER', '{"host":"127.0.0.1","port":"6379","password":"","group":"0","pconnect":"y"}');//主库
//define('SERVER_REDIS_SLAVE', '{"host":"127.0.0.1","port":"6379","password":"123456","group":"0","pconnect":"y"}');//从库
//define('REDIS_USE_SLAVE_METHODS', '');//redis 走从库的方法设置 方法名（逗号隔开，和redisx类中名称保持一致）

//oss配置
define('OSS', ['endpoint' => 'oss-cn-hangzhou.aliyuncs.com','accessKeyId' => 'xxx','accessKeySecret' => 'xxx','bucket' => 'xxxx']);

//自动上传日志到OSS配置,需配合_log方法使用
//define('OSS_LOG','{"accessKeyId":"xxx","accessKeySecret":"xxx","endpoint":"http://oss-cn-hangzhou.aliyuncs.com","bucket":"xxxx"}');

//敏感key默认配置
define('JWT_KEY', 'jumingjwtkey');//JWT类的密钥 使用jwt模式,必须修改该值
//define('MI_KEY','mikeyxxx');//jiami/jiemi函数的默认KEY
define('X_KEY', 'xxxxxx');//系统敏感配置 jiami/jiemi KEY, 注意！需要自行修改密钥

define('USER_LOG', 1);//是否记录用户请求日志
define('ADMIN_TOKEN_TIME', 0);//是否开启TOKEN模式 0不开启，大于0则是超多少秒TOKEN过期
define('BENDI', 1);//是否本地开发环境
